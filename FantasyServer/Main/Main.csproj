<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\Entity\Entity.csproj" />
      <ProjectReference Include="..\Fantasy.Net\Fantasy.Net\Fantasy.Net.csproj" />
<!--      <ProjectReference Include="..\Fantasy.Packages\Fantasy.ConfigTable\Net\Fantasy.ConfigTable.csproj" />-->
      <ProjectReference Include="..\Fantasy.Packages\Fantasy.NLog\Fantasy.NLog.csproj" />
      <ProjectReference Include="..\Hotfix\Hotfix.csproj" />
    </ItemGroup>

</Project>
