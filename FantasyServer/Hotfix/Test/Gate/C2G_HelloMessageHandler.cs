
using Fantasy;
using Fantasy.Async;
using Fantasy.Network;
using Fantasy.Network.Interface;
using Fantasy.Platform.Net;

namespace Demo
{
    public class C2G_HelloMessageHandler : Message<C2G_HelloMessage>
    {
        protected override async FTask Run(Session session, C2G_HelloMessage message)
        {
            Log.Info($"Gate received client message: {message.Tag}");
            session.Send(new G2C_HelloMessage(){Tag = "Message, hello client!"});
            
            // 注册中转路由
            var routeComponent = session.AddComponent<RouteComponent>();
            
            var chatSceneConfigs = SceneConfigData.Instance.GetSceneBySceneType(SceneType.Chat);
            var chatSceneConfig = chatSceneConfigs[0];
            session.Scene.NetworkMessagingComponent.SendInnerRoute(chatSceneConfig.RouteId, new G2Chat_HelloMessage()
            {
                Tag = "Message, hello chat!",
            });
            var resp = await session.Scene.NetworkMessagingComponent.CallInnerRoute(chatSceneConfig.RouteId, new G2Chat_HelloRequest()
            {
                Tag = "Request, hello chat!",
                RouteId = session.RouteId
            }) as Chat2G_HelloResponse;
            if (resp == null)
            {
                Log.Info("Chat response is null, cannot register route.");
                return;
            }
            Log.Info($"Gate received chat response: {resp.Tag} {resp.RouteId}");
            // 注册聊天中转
            routeComponent.AddAddress(RouteType.ChatRoute, resp.RouteId);
            
            await FTask.CompletedTask;
        }
    }
}
