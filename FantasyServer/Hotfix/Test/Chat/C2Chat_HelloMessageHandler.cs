using Fantasy;
using Fantasy.Async;
using Fantasy.Network.Interface;

namespace Demo
{
    public class C2Chat_HelloMessageHandler : Route<ChatEntity, C2Chat_HelloMessage>
    {
        protected override async FTask Run(ChatEntity entity, C2Chat_HelloMessage message)
        {
            Log.Info($"Chat received client message: {message.Tag}");
            entity.Scene.NetworkMessagingComponent.SendInnerRoute(entity.GateRouteId, new Chat2C_HelloMessage()
            {
                Tag = "Message, hello client!",
            });
            await FTask.CompletedTask;
        }
    }
}
