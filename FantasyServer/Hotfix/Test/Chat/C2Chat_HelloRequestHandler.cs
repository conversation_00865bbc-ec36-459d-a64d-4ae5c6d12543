using Fantasy;
using Fantasy.Async;
using Fantasy.Network.Interface;

namespace Demo
{
    public class C2Chat_HelloRequestHandler : RouteRPC<ChatEntity, C2Chat_HelloRequest, Chat2C_HelloResponse>
    {
        protected override async FTask Run(ChatEntity entity, C2Chat_HelloRequest request, Chat2C_HelloResponse response, Action reply)
        {
            Log.Info($"Chat received client request: {request.Tag}");
            response.Tag = $"Chat response to: {request.Tag}";
            await FTask.CompletedTask;
        }
    }
}
