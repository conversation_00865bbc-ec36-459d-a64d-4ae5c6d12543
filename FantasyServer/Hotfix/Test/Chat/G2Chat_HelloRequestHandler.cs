using Fantasy;
using Fantasy.Async;
using Fantasy.Entitas;
using Fantasy.Network.Interface;

namespace Demo
{
    public class G2Chat_HelloRequestHandler : RouteRPC<Scene, G2Chat_HelloRequest, Chat2G_HelloResponse>
    {
        protected override async FTask Run(Scene scene, G2Chat_HelloRequest request, Chat2G_HelloResponse response, Action reply)
        {
            var chatEntity = Entity.Create<ChatEntity>(scene, true, true);
            chatEntity.GateRouteId = request.RouteId;
            Log.Info($"Chat received gate request: {request.Tag}");
            response.Tag = $"Chat response to: {request.Tag}";
            response.RouteId = chatEntity.RouteId;
            await FTask.CompletedTask;
        }
    }
}
