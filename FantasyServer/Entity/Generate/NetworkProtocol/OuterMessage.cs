using ProtoBuf;

using System.Collections.Generic;
using MongoDB.Bson.Serialization.Attributes;
using Fantasy;
using Fantasy.Network.Interface;
using Fantasy.Serialize;
// <PERSON>S<PERSON>per disable InconsistentNaming
// ReSharper disable RedundantUsingDirective
// ReSharper disable RedundantOverriddenMember
// ReS<PERSON>per disable PartialTypeWithSinglePart
// ReSharper disable UnusedAutoPropertyAccessor.Global
// ReSharper disable MemberCanBePrivate.Global
// ReSharper disable CheckNamespace
#pragma warning disable CS8625 // Cannot convert null literal to non-nullable reference type.
#pragma warning disable CS8618

namespace Fantasy
{	
	[ProtoContract]
	public partial class C2G_HelloMessage : AMessage, IMessage, IProto
	{
		public static C2G_HelloMessage Create(Scene scene)
		{
			return scene.MessagePoolComponent.Rent<C2G_HelloMessage>();
		}
		public override void Dispose()
		{
			Tag = default;
#if FANTASY_NET || FANTASY_UNITY
			GetScene().MessagePoolComponent.Return<C2G_HelloMessage>(this);
#endif
		}
		public uint OpCode() { return OuterOpcode.C2G_HelloMessage; }
		[ProtoMember(1)]
		public string Tag { get; set; }
	}
	[ProtoContract]
	public partial class G2C_HelloMessage : AMessage, IMessage, IProto
	{
		public static G2C_HelloMessage Create(Scene scene)
		{
			return scene.MessagePoolComponent.Rent<G2C_HelloMessage>();
		}
		public override void Dispose()
		{
			Tag = default;
#if FANTASY_NET || FANTASY_UNITY
			GetScene().MessagePoolComponent.Return<G2C_HelloMessage>(this);
#endif
		}
		public uint OpCode() { return OuterOpcode.G2C_HelloMessage; }
		[ProtoMember(1)]
		public string Tag { get; set; }
	}
	[ProtoContract]
	public partial class C2G_HelloRequest : AMessage, IRequest, IProto
	{
		public static C2G_HelloRequest Create(Scene scene)
		{
			return scene.MessagePoolComponent.Rent<C2G_HelloRequest>();
		}
		public override void Dispose()
		{
			Tag = default;
#if FANTASY_NET || FANTASY_UNITY
			GetScene().MessagePoolComponent.Return<C2G_HelloRequest>(this);
#endif
		}
		[ProtoIgnore]
		public G2C_HelloResponse ResponseType { get; set; }
		public uint OpCode() { return OuterOpcode.C2G_HelloRequest; }
		[ProtoMember(1)]
		public string Tag { get; set; }
	}
	[ProtoContract]
	public partial class G2C_HelloResponse : AMessage, IResponse, IProto
	{
		public static G2C_HelloResponse Create(Scene scene)
		{
			return scene.MessagePoolComponent.Rent<G2C_HelloResponse>();
		}
		public override void Dispose()
		{
			ErrorCode = default;
			Tag = default;
#if FANTASY_NET || FANTASY_UNITY
			GetScene().MessagePoolComponent.Return<G2C_HelloResponse>(this);
#endif
		}
		public uint OpCode() { return OuterOpcode.G2C_HelloResponse; }
		[ProtoMember(1)]
		public string Tag { get; set; }
		[ProtoMember(2)]
		public uint ErrorCode { get; set; }
	}
	[ProtoContract]
	public partial class C2Chat_HelloMessage : AMessage, ICustomRouteMessage, IProto
	{
		public static C2Chat_HelloMessage Create(Scene scene)
		{
			return scene.MessagePoolComponent.Rent<C2Chat_HelloMessage>();
		}
		public override void Dispose()
		{
			Tag = default;
#if FANTASY_NET || FANTASY_UNITY
			GetScene().MessagePoolComponent.Return<C2Chat_HelloMessage>(this);
#endif
		}
		public uint OpCode() { return OuterOpcode.C2Chat_HelloMessage; }
		[ProtoIgnore]
		public int RouteType => Fantasy.RouteType.ChatRoute;
		[ProtoMember(1)]
		public string Tag { get; set; }
	}
	[ProtoContract]
	public partial class Chat2C_HelloMessage : AMessage, ICustomRouteMessage, IProto
	{
		public static Chat2C_HelloMessage Create(Scene scene)
		{
			return scene.MessagePoolComponent.Rent<Chat2C_HelloMessage>();
		}
		public override void Dispose()
		{
			Tag = default;
#if FANTASY_NET || FANTASY_UNITY
			GetScene().MessagePoolComponent.Return<Chat2C_HelloMessage>(this);
#endif
		}
		public uint OpCode() { return OuterOpcode.Chat2C_HelloMessage; }
		[ProtoIgnore]
		public int RouteType => Fantasy.RouteType.ChatRoute;
		[ProtoMember(1)]
		public string Tag { get; set; }
	}
	[ProtoContract]
	public partial class C2Chat_HelloRequest : AMessage, ICustomRouteRequest, IProto
	{
		public static C2Chat_HelloRequest Create(Scene scene)
		{
			return scene.MessagePoolComponent.Rent<C2Chat_HelloRequest>();
		}
		public override void Dispose()
		{
			Tag = default;
#if FANTASY_NET || FANTASY_UNITY
			GetScene().MessagePoolComponent.Return<C2Chat_HelloRequest>(this);
#endif
		}
		[ProtoIgnore]
		public Chat2C_HelloResponse ResponseType { get; set; }
		public uint OpCode() { return OuterOpcode.C2Chat_HelloRequest; }
		[ProtoIgnore]
		public int RouteType => Fantasy.RouteType.ChatRoute;
		[ProtoMember(1)]
		public string Tag { get; set; }
	}
	[ProtoContract]
	public partial class Chat2C_HelloResponse : AMessage, ICustomRouteResponse, IProto
	{
		public static Chat2C_HelloResponse Create(Scene scene)
		{
			return scene.MessagePoolComponent.Rent<Chat2C_HelloResponse>();
		}
		public override void Dispose()
		{
			ErrorCode = default;
			Tag = default;
#if FANTASY_NET || FANTASY_UNITY
			GetScene().MessagePoolComponent.Return<Chat2C_HelloResponse>(this);
#endif
		}
		public uint OpCode() { return OuterOpcode.Chat2C_HelloResponse; }
		[ProtoMember(1)]
		public string Tag { get; set; }
		[ProtoMember(2)]
		public uint ErrorCode { get; set; }
	}
}
