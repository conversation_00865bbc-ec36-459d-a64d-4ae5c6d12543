<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <AssemblyName>Fantasy-Net</AssemblyName>
        <TargetFrameworks>net8.0;net9.0</TargetFrameworks>
        <LangVersion>default</LangVersion>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
      <DefineConstants>TRACE;FANTASY_NET</DefineConstants>
      <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
      <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
      <DocumentationFile>bin\Debug\$(TargetFramework)\Fantasy.Net.xml</DocumentationFile>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
      <DefineConstants>TRACE;FANTASY_NET</DefineConstants>
      <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="CommandLineParser" Version="2.9.1" />
      <FrameworkReference Include="Microsoft.AspNetCore.App"/>
      <PackageReference Include="mimalloc-csharp" Version="1.0.7" />
      <PackageReference Include="MongoDB.Bson" Version="3.1.0" />
      <PackageReference Include="MongoDB.Driver" Version="3.1.0" />
      <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
      <PackageReference Include="protobuf-net" Version="3.2.45" />
    </ItemGroup>

    <ItemGroup Condition="'$(TargetFramework)' == 'net8.0'">
        <PackageReference Include="System.IO.Pipelines" Version="9.0.0" />
    </ItemGroup>

</Project>
