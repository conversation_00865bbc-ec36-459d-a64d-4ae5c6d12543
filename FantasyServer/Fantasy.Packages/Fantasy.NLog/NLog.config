<?xml version="1.0" encoding="utf-8"?>

<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:schemaLocation="http://www.nlog-project.org/schemas/NLog.xsd NLog.xsd">
    <targets async="true">
        <target name="ServerDebug" xsi:type="File"
                encoding="UTF-8"
                createDirs="true"
                autoFlush="false"
                keepFileOpen="true"
                concurrentWrites="true"
                openFileCacheTimeout="30"
                openFileFlushTimeout="60"
                fileName="${basedir}/../Logs/Server/Server${date:format=yyyyMMdd}/${logger}.${var:appId}.${date:format=yyyyMMddHH}.Debug.log"
                layout="${longdate} ${callsite:className=false:methodName=false:fileName=true:includeSourcePath=false:skipFrames=2} ${message}" />
    </targets>
    <targets async="true">
        <target name="ServerInfo" xsi:type="File"
                encoding="UTF-8"
                createDirs="true"
                autoFlush="false"
                keepFileOpen="true"
                concurrentWrites="true"
                openFileCacheTimeout="30"
                openFileFlushTimeout="60"
                fileName="${basedir}/../Logs/Server/Server${date:format=yyyyMMdd}/${logger}.${var:appId}.${date:format=yyyyMMddHH}.Info.log"
                layout="${longdate} ${callsite:className=false:methodName=false:fileName=true:includeSourcePath=false:skipFrames=2} ${message}" />
    </targets>
    <targets async="true">
        <target name="ServerWarn" xsi:type="File"
                encoding="UTF-8"
                createDirs="true"
                autoFlush="false"
                keepFileOpen="true"
                concurrentWrites="true"
                openFileCacheTimeout="30"
                openFileFlushTimeout="60"
                fileName="${basedir}/../Logs/Server/Server${date:format=yyyyMMdd}/${logger}.${var:appId}.${date:format=yyyyMMddHH}.Warn.log"
                layout="${longdate} ${callsite:className=false:methodName=false:fileName=true:includeSourcePath=false:skipFrames=2} ${message}" />
    </targets>
    <targets async="true">
        <target name="ServerError" xsi:type="File"
                encoding="UTF-8"
                createDirs="true"
                autoFlush="false"
                keepFileOpen="true"
                concurrentWrites="true"
                openFileCacheTimeout="30"
                openFileFlushTimeout="60"
                fileName="${basedir}/../Logs/Server/Server${date:format=yyyyMMdd}/${logger}.${var:appId}.${date:format=yyyyMMddHH}.Error.log"
                layout="${longdate} ${callsite:className=false:methodName=false:fileName=true:includeSourcePath=false:skipFrames=2} ${message}" />
    </targets>
    <targets async="true">
        <target name="ServerTrace" xsi:type="File"
                encoding="UTF-8"
                createDirs="true"
                autoFlush="false"
                keepFileOpen="true"
                concurrentWrites="true"
                openFileCacheTimeout="30"
                openFileFlushTimeout="60"
                fileName="${basedir}/../Logs/Server/Server${date:format=yyyyMMdd}/${logger}.${var:appId}.${date:format=yyyyMMddHH}.Trace.log"
                layout="${longdate} ${callsite:className=false:methodName=false:fileName=true:includeSourcePath=false:skipFrames=2} ${message}" />
    </targets>
    <targets async="true">
        <target name="ConsoleColor" xsi:type="ColoredConsole"
                useDefaultRowHighlightingRules="false"
                layout="${longdate} ${callsite:className=false:methodName=false:fileName=true:includeSourcePath=false:skipFrames=2} ${message}">
            <highlight-row condition="level == LogLevel.Debug" foregroundColor="DarkGreen" />
            <highlight-row condition="level == LogLevel.Info" foregroundColor="Gray" />
            <highlight-row condition="level == LogLevel.Warn" foregroundColor="Yellow" />
            <highlight-row condition="level == LogLevel.Error" foregroundColor="DarkRed" />
            <highlight-row condition="level == LogLevel.Fatal" foregroundColor="Red" />
        </target>
    </targets>
    <rules>
        <!-- 控制台 调试或编辑器启动的时候会调用-->
        <logger ruleName="ConsoleTrace" name="Server" level="Trace" writeTo="ConsoleColor" />
        <logger ruleName="ConsoleDebug" name="Server" level="Debug" writeTo="ConsoleColor" />
        <logger ruleName="ConsoleInfo" name="Server" level="Info" writeTo="ConsoleColor" />
        <logger ruleName="ConsoleWarn" name="Server" level="Warn" writeTo="ConsoleColor" />
        <logger ruleName="ConsoleError" name="Server" level="Error" writeTo="ConsoleColor" />
        <!-- 服务端日志输出文件 发布到服务器后会调用-->
        <logger ruleName="ServerDebug" name="Server" level="Debug" writeTo="ServerDebug" />
        <logger ruleName="ServerTrace" name="Server" level="Trace" writeTo="ServerTrace" />
        <logger ruleName="ServerInfo" name="Server" level="Info" writeTo="ServerInfo" />
        <logger ruleName="ServerWarn" name="Server" level="Warn" writeTo="ServerWarn" />
        <logger ruleName="ServerError" name="Server" level="Error" writeTo="ServerError" />
    </rules>
</nlog>