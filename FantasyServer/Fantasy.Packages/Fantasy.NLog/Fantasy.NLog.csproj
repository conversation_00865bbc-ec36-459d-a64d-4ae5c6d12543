<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <AssemblyName>Fantasy-Net.NLog</AssemblyName>
        <TargetFrameworks>net8.0;net9.0</TargetFrameworks>
        <LangVersion>default</LangVersion>
    </PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\Fantasy.Net\Fantasy.Net\Fantasy.Net.csproj" />
      <PackageReference Include="NLog" Version="5.3.4" />
    </ItemGroup>

    <!-- 直接包含配置文件，自动复制到输出目录 -->
    <ItemGroup>
        <None Include="NLog.config">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Include="NLog.xsd">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
    </ItemGroup>

</Project>
